<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/surface"
    android:elevation="8dp">

    <!-- Progress Bar at the top -->
    <ProgressBar
        android:id="@+id/miniProgressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:progressTint="@color/primary"
        android:progressBackgroundTint="@color/progress_background"
        tools:progress="45" />

    <!-- Main content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical"
        android:foreground="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true">

        <!-- Album Artwork (Square with rounded corners) -->
        <ImageView
            android:id="@+id/miniAlbumArtwork"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:scaleType="centerCrop"
            android:background="@drawable/placeholder_album_art"
            tools:src="@drawable/placeholder_album_art" />

        <!-- Song Info Container -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:orientation="vertical">

            <!-- Song Title -->
            <TextView
                android:id="@+id/miniSongTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="September" />

            <!-- Artist Name -->
            <TextView
                android:id="@+id/miniArtistName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                tools:text="Earth, Wind &amp; Fire" />

        </LinearLayout>

        <!-- Control Buttons -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageButton
                android:id="@+id/miniPreviousButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_skip_previous"
                android:contentDescription="@string/previous"
                android:tint="@color/text_primary" />

            <ImageButton
                android:id="@+id/miniPlayPauseButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginHorizontal="4dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_pause"
                android:contentDescription="@string/pause"
                android:tint="@color/text_primary" />

            <ImageButton
                android:id="@+id/miniNextButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_skip_next"
                android:contentDescription="@string/next"
                android:tint="@color/text_primary" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
