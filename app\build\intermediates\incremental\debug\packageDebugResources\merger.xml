<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android Projects\MusicPlayer\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android Projects\MusicPlayer\app\src\main\res"><file name="ic_arrow_back" path="D:\Android Projects\MusicPlayer\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_equalizer" path="D:\Android Projects\MusicPlayer\app\src\main\res\drawable\ic_equalizer.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\Android Projects\MusicPlayer\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\Android Projects\MusicPlayer\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_more_vert" path="D:\Android Projects\MusicPlayer\app\src\main\res\drawable\ic_more_vert.xml" qualifiers="" type="drawable"/><file name="ic_music_note" path="D:\Android Projects\MusicPlayer\app\src\main\res\drawable\ic_music_note.xml" qualifiers="" type="drawable"/><file name="ic_pause" path="D:\Android Projects\MusicPlayer\app\src\main\res\drawable\ic_pause.xml" qualifiers="" type="drawable"/><file name="ic_play_arrow" path="D:\Android Projects\MusicPlayer\app\src\main\res\drawable\ic_play_arrow.xml" qualifiers="" type="drawable"/><file name="ic_repeat" path="D:\Android Projects\MusicPlayer\app\src\main\res\drawable\ic_repeat.xml" qualifiers="" type="drawable"/><file name="ic_search" path="D:\Android Projects\MusicPlayer\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_shuffle" path="D:\Android Projects\MusicPlayer\app\src\main\res\drawable\ic_shuffle.xml" qualifiers="" type="drawable"/><file name="ic_skip_next" path="D:\Android Projects\MusicPlayer\app\src\main\res\drawable\ic_skip_next.xml" qualifiers="" type="drawable"/><file name="ic_skip_previous" path="D:\Android Projects\MusicPlayer\app\src\main\res\drawable\ic_skip_previous.xml" qualifiers="" type="drawable"/><file name="placeholder_album_art" path="D:\Android Projects\MusicPlayer\app\src\main\res\drawable\placeholder_album_art.xml" qualifiers="" type="drawable"/><file name="activity_main" path="D:\Android Projects\MusicPlayer\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_player" path="D:\Android Projects\MusicPlayer\app\src\main\res\layout\activity_player.xml" qualifiers="" type="layout"/><file name="item_song" path="D:\Android Projects\MusicPlayer\app\src\main\res\layout\item_song.xml" qualifiers="" type="layout"/><file name="mini_player" path="D:\Android Projects\MusicPlayer\app\src\main\res\layout\mini_player.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\Android Projects\MusicPlayer\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\Android Projects\MusicPlayer\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\Android Projects\MusicPlayer\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Android Projects\MusicPlayer\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Android Projects\MusicPlayer\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Android Projects\MusicPlayer\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Android Projects\MusicPlayer\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Android Projects\MusicPlayer\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Android Projects\MusicPlayer\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Android Projects\MusicPlayer\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Android Projects\MusicPlayer\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Android Projects\MusicPlayer\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\Android Projects\MusicPlayer\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary">#E91E63</color><color name="primary_dark">#C2185B</color><color name="primary_light">#F48FB1</color><color name="background_primary">#121212</color><color name="background_secondary">#1E1E1E</color><color name="surface">#2C2C2C</color><color name="text_primary">#FFFFFF</color><color name="text_secondary">#B3B3B3</color><color name="text_tertiary">#808080</color><color name="progress_background">#404040</color><color name="accent">#FF6B6B</color><color name="error">#F44336</color><color name="success">#4CAF50</color><color name="warning">#FF9800</color><color name="headphone_red">#F44336</color><color name="surface_variant">#3C3C3C</color><color name="headphone_pink">#E91E63</color></file><file path="D:\Android Projects\MusicPlayer\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Music Player</string><string name="search_hint">Search songs, artists, albums…</string><string name="no_songs_found">No songs found</string><string name="try_refreshing">Try refreshing or check your connection</string><string name="now_playing">Now Playing</string><string name="play">Play</string><string name="pause">Pause</string><string name="previous">Previous</string><string name="next">Next</string><string name="shuffle">Shuffle</string><string name="repeat">Repeat</string><string name="more_options">More options</string><string name="notification_channel_name">Music Playback</string><string name="notification_channel_description">Controls for music playback</string><string name="error_loading_songs">Failed to load songs</string><string name="error_network">Network error. Please check your connection.</string><string name="error_playback">Playback error occurred</string><string name="album_artwork">Album artwork</string><string name="playing_indicator">Currently playing</string></file><file path="D:\Android Projects\MusicPlayer\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.MusicPlayer" parent="Theme.Material3.Dark.NoActionBar">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>

        
        <item name="android:colorBackground">@color/background_primary</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/text_primary</item>

        
        <item name="android:statusBarColor">@color/background_primary</item>
        <item name="android:windowLightStatusBar">false</item>

        
        <item name="android:navigationBarColor">@color/background_primary</item>

        
        <item name="android:windowBackground">@color/background_primary</item>
    </style><style name="Theme.MusicPlayer" parent="Base.Theme.MusicPlayer"/></file><file path="D:\Android Projects\MusicPlayer\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.MusicPlayer" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="D:\Android Projects\MusicPlayer\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\Android Projects\MusicPlayer\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="ic_repeat_one" path="D:\Android Projects\MusicPlayer\app\src\main\res\drawable\ic_repeat_one.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android Projects\MusicPlayer\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android Projects\MusicPlayer\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android Projects\MusicPlayer\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Android Projects\MusicPlayer\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>