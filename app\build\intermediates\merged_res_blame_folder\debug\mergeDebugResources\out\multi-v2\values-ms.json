{"logs": [{"outputFile": "com.nauh.musicplayer.app-mergeDebugResources-41:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c6ed593d095e40b444f65ff3ae72d38a\\transformed\\material-1.10.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,279,359,438,525,617,704,807,923,1006,1071,1164,1229,1288,1375,1437,1499,1559,1625,1687,1741,1849,1906,1967,2022,2093,2213,2304,2390,2538,2624,2710,2838,2926,3004,3057,3108,3174,3245,3323,3406,3485,3558,3634,3707,3778,3885,3977,4050,4140,4233,4307,4378,4469,4521,4601,4669,4753,4838,4900,4964,5027,5099,5203,5311,5407,5513,5570,5625", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,79,78,86,91,86,102,115,82,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,85,147,85,85,127,87,77,52,50,65,70,77,82,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85", "endOffsets": "274,354,433,520,612,699,802,918,1001,1066,1159,1224,1283,1370,1432,1494,1554,1620,1682,1736,1844,1901,1962,2017,2088,2208,2299,2385,2533,2619,2705,2833,2921,2999,3052,3103,3169,3240,3318,3401,3480,3553,3629,3702,3773,3880,3972,4045,4135,4228,4302,4373,4464,4516,4596,4664,4748,4833,4895,4959,5022,5094,5198,5306,5402,5508,5565,5620,5706"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,67,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "738,3686,3766,3845,3932,4024,4854,4957,5073,5235,9012,9105,9170,9229,9316,9378,9440,9500,9566,9628,9682,9790,9847,9908,9963,10034,10638,10729,10815,10963,11049,11135,11263,11351,11429,11482,11533,11599,11670,11748,11831,11910,11983,12059,12132,12203,12310,12402,12475,12565,12658,12732,12803,12894,12946,13026,13094,13178,13263,13325,13389,13452,13524,13628,13736,13832,13938,13995,14050", "endLines": "22,51,52,53,54,55,63,64,65,67,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "endColumns": "12,79,78,86,91,86,102,115,82,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,85,147,85,85,127,87,77,52,50,65,70,77,82,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85", "endOffsets": "912,3761,3840,3927,4019,4106,4952,5068,5151,5295,9100,9165,9224,9311,9373,9435,9495,9561,9623,9677,9785,9842,9903,9958,10029,10149,10724,10810,10958,11044,11130,11258,11346,11424,11477,11528,11594,11665,11743,11826,11905,11978,12054,12127,12198,12305,12397,12470,12560,12653,12727,12798,12889,12941,13021,13089,13173,13258,13320,13384,13447,13519,13623,13731,13827,13933,13990,14045,14131"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8c6e034149c65399529a9cd0f184f8b\\transformed\\media3-session-1.2.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,129,208,275,341,414,490,589", "endColumns": "73,78,66,65,72,75,98,102", "endOffsets": "124,203,270,336,409,485,584,687"}, "to": {"startLines": "50,66,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3612,5156,10154,10221,10287,10360,10436,10535", "endColumns": "73,78,66,65,72,75,98,102", "endOffsets": "3681,5230,10216,10282,10355,10431,10530,10633"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\87c47c7abe7f29a54b096c7ff4d96851\\transformed\\core-1.16.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "56,57,58,59,60,61,62,185", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4111,4206,4308,4405,4515,4621,4739,14217", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "4201,4303,4400,4510,4616,4734,4849,14313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e355fbe8fe59cc5407000c654ab4b66\\transformed\\appcompat-1.6.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "917,1028,1133,1241,1328,1432,1543,1622,1700,1791,1884,1979,2073,2171,2264,2359,2453,2544,2635,2715,2827,2935,3032,3141,3245,3352,3511,14136", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "1023,1128,1236,1323,1427,1538,1617,1695,1786,1879,1974,2068,2166,2259,2354,2448,2539,2630,2710,2822,2930,3027,3136,3240,3347,3506,3607,14212"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c077dfbc8dc20562e970eb684d92a281\\transformed\\media3-ui-1.2.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,471,643,726,810,887,978,1071,1144,1213,1309,1403,1467,1530,1595,1668,1774,1883,1988,2055,2137,2207,2278,2362,2447,2514,2577,2630,2688,2736,2797,2861,2923,2984,3050,3113,3172,3238,3302,3368,3420,3482,3558,3634", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,105,108,104,66,81,69,70,83,84,66,62,52,57,47,60,63,61,60,65,62,58,65,63,65,51,61,75,75,61", "endOffsets": "279,466,638,721,805,882,973,1066,1139,1208,1304,1398,1462,1525,1590,1663,1769,1878,1983,2050,2132,2202,2273,2357,2442,2509,2572,2625,2683,2731,2792,2856,2918,2979,3045,3108,3167,3233,3297,3363,3415,3477,3553,3629,3691"}, "to": {"startLines": "2,11,15,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,379,566,5300,5383,5467,5544,5635,5728,5801,5870,5966,6060,6124,6187,6252,6325,6431,6540,6645,6712,6794,6864,6935,7019,7104,7171,7893,7946,8004,8052,8113,8177,8239,8300,8366,8429,8488,8554,8618,8684,8736,8798,8874,8950", "endLines": "10,14,18,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118", "endColumns": "17,12,12,82,83,76,90,92,72,68,95,93,63,62,64,72,105,108,104,66,81,69,70,83,84,66,62,52,57,47,60,63,61,60,65,62,58,65,63,65,51,61,75,75,61", "endOffsets": "374,561,733,5378,5462,5539,5630,5723,5796,5865,5961,6055,6119,6182,6247,6320,6426,6535,6640,6707,6789,6859,6930,7014,7099,7166,7229,7941,7999,8047,8108,8172,8234,8295,8361,8424,8483,8549,8613,8679,8731,8793,8869,8945,9007"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d41d36b14e47c257e85459df73d97ebd\\transformed\\media3-exoplayer-1.2.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,189,255,320,398,464,554,637", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "120,184,250,315,393,459,549,632,709"}, "to": {"startLines": "92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7234,7304,7368,7434,7499,7577,7643,7733,7816", "endColumns": "69,63,65,64,77,65,89,82,76", "endOffsets": "7299,7363,7429,7494,7572,7638,7728,7811,7888"}}]}]}