<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    android:foreground="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <!-- Album Artwork -->
        <ImageView
            android:id="@+id/albumArtwork"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:scaleType="centerCrop"
            android:background="@drawable/placeholder_album_art"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:src="@drawable/placeholder_album_art" />

        <!-- Song Title -->
        <TextView
            android:id="@+id/songTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/songDuration"
            app:layout_constraintStart_toEndOf="@id/albumArtwork"
            app:layout_constraintTop_toTopOf="@id/albumArtwork"
            tools:text="Song Title" />

        <!-- Artist and Album -->
        <TextView
            android:id="@+id/artistAlbum"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            app:layout_constraintEnd_toStartOf="@id/songDuration"
            app:layout_constraintStart_toEndOf="@id/albumArtwork"
            app:layout_constraintTop_toBottomOf="@id/songTitle"
            tools:text="Artist • Album" />

        <!-- Song Duration -->
        <TextView
            android:id="@+id/songDuration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@id/moreOptions"
            app:layout_constraintTop_toTopOf="@id/albumArtwork"
            tools:text="3:45" />

        <!-- More Options Button -->
        <ImageButton
            android:id="@+id/moreOptions"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_more_vert"
            android:contentDescription="@string/more_options"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/albumArtwork"
            app:tint="@color/text_secondary" />

        <!-- Playing Indicator -->
        <ImageView
            android:id="@+id/playingIndicator"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginEnd="8dp"
            android:src="@drawable/ic_equalizer"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/albumArtwork"
            app:layout_constraintEnd_toStartOf="@id/moreOptions"
            app:tint="@color/primary"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
