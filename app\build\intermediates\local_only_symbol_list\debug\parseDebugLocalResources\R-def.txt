R_DEF: Internal format may change without notice
local
color accent
color background_primary
color background_secondary
color black
color error
color headphone_pink
color headphone_red
color primary
color primary_dark
color primary_light
color progress_background
color success
color surface
color surface_variant
color text_primary
color text_secondary
color text_tertiary
color warning
color white
drawable ic_arrow_back
drawable ic_equalizer
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_more_vert
drawable ic_music_note
drawable ic_pause
drawable ic_play_arrow
drawable ic_repeat
drawable ic_repeat_one
drawable ic_search
drawable ic_shuffle
drawable ic_skip_next
drawable ic_skip_previous
drawable placeholder_album_art
id albumArtwork
id albumArtworkCard
id artistAlbum
id controlButtonsLayout
id currentTime
id emptyStateLayout
id main
id miniAlbumArtwork
id miniArtistName
id miniNextButton
id miniPlayPauseButton
id miniPlayer
id miniPreviousButton
id miniProgressBar
id miniSongTitle
id moreOptions
id nextButton
id playPauseButton
id playerAlbumArtwork
id playerAlbumName
id playerArtistName
id playerProgressBar
id playerSongTitle
id playerToolbar
id playingIndicator
id previousButton
id progressBar
id progressLayout
id repeatButton
id searchCard
id searchEditText
id seekBar
id shuffleButton
id songDuration
id songInfoLayout
id songTitle
id songsRecyclerView
id toolbar
id totalTime
layout activity_main
layout activity_player
layout item_song
layout mini_player
mipmap ic_launcher
mipmap ic_launcher_round
string album_artwork
string app_name
string error_loading_songs
string error_network
string error_playback
string more_options
string next
string no_songs_found
string notification_channel_description
string notification_channel_name
string now_playing
string pause
string play
string playing_indicator
string previous
string repeat
string search_hint
string shuffle
string try_refreshing
style Base.Theme.MusicPlayer
style Theme.MusicPlayer
xml backup_rules
xml data_extraction_rules
