{"logs": [{"outputFile": "com.nauh.musicplayer.app-mergeDebugResources-41:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\87c47c7abe7f29a54b096c7ff4d96851\\transformed\\core-1.16.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "56,57,58,59,60,61,62,185", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4143,4237,4339,4436,4537,4644,4751,14484", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "4232,4334,4431,4532,4639,4746,4861,14580"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e355fbe8fe59cc5407000c654ab4b66\\transformed\\appcompat-1.6.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "960,1063,1162,1270,1360,1465,1582,1665,1747,1838,1931,2026,2120,2220,2313,2408,2502,2593,2684,2766,2867,2975,3074,3181,3293,3397,3559,14401", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "1058,1157,1265,1355,1460,1577,1660,1742,1833,1926,2021,2115,2215,2308,2403,2497,2588,2679,2761,2862,2970,3069,3176,3288,3392,3554,3651,14479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c6ed593d095e40b444f65ff3ae72d38a\\transformed\\material-1.10.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,335,409,482,579,668,767,896,979,1047,1139,1212,1275,1361,1423,1486,1551,1619,1682,1736,1868,1925,1987,2041,2115,2253,2334,2414,2546,2631,2718,2859,2947,3026,3080,3133,3199,3271,3353,3443,3528,3600,3675,3746,3819,3925,4022,4096,4191,4288,4362,4447,4547,4600,4685,4753,4841,4931,4993,5057,5120,5187,5304,5416,5527,5638,5696,5753", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,73,72,96,88,98,128,82,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,131,84,86,140,87,78,53,52,65,71,81,89,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80", "endOffsets": "254,330,404,477,574,663,762,891,974,1042,1134,1207,1270,1356,1418,1481,1546,1614,1677,1731,1863,1920,1982,2036,2110,2248,2329,2409,2541,2626,2713,2854,2942,3021,3075,3128,3194,3266,3348,3438,3523,3595,3670,3741,3814,3920,4017,4091,4186,4283,4357,4442,4542,4595,4680,4748,4836,4926,4988,5052,5115,5182,5299,5411,5522,5633,5691,5748,5829"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,67,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "801,3734,3810,3884,3957,4054,4866,4965,5094,5255,9106,9198,9271,9334,9420,9482,9545,9610,9678,9741,9795,9927,9984,10046,10100,10174,10820,10901,10981,11113,11198,11285,11426,11514,11593,11647,11700,11766,11838,11920,12010,12095,12167,12242,12313,12386,12492,12589,12663,12758,12855,12929,13014,13114,13167,13252,13320,13408,13498,13560,13624,13687,13754,13871,13983,14094,14205,14263,14320", "endLines": "22,51,52,53,54,55,63,64,65,67,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183", "endColumns": "12,75,73,72,96,88,98,128,82,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,131,84,86,140,87,78,53,52,65,71,81,89,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80", "endOffsets": "955,3805,3879,3952,4049,4138,4960,5089,5172,5318,9193,9266,9329,9415,9477,9540,9605,9673,9736,9790,9922,9979,10041,10095,10169,10307,10896,10976,11108,11193,11280,11421,11509,11588,11642,11695,11761,11833,11915,12005,12090,12162,12237,12308,12381,12487,12584,12658,12753,12850,12924,13009,13109,13162,13247,13315,13403,13493,13555,13619,13682,13749,13866,13978,14089,14200,14258,14315,14396"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c077dfbc8dc20562e970eb684d92a281\\transformed\\media3-ui-1.2.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,289,506,706,785,862,947,1037,1125,1201,1267,1360,1455,1522,1586,1647,1722,1835,1952,2065,2139,2220,2293,2371,2462,2551,2619,2697,2750,2808,2856,2917,2978,3045,3109,3175,3238,3297,3363,3432,3498,3550,3616,3699,3782", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,78,76,84,89,87,75,65,92,94,66,63,60,74,112,116,112,73,80,72,77,90,88,67,77,52,57,47,60,60,66,63,65,62,58,65,68,65,51,65,82,82,57", "endOffsets": "284,501,701,780,857,942,1032,1120,1196,1262,1355,1450,1517,1581,1642,1717,1830,1947,2060,2134,2215,2288,2366,2457,2546,2614,2692,2745,2803,2851,2912,2973,3040,3104,3170,3233,3292,3358,3427,3493,3545,3611,3694,3777,3835"}, "to": {"startLines": "2,11,15,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,384,601,5323,5402,5479,5564,5654,5742,5818,5884,5977,6072,6139,6203,6264,6339,6452,6569,6682,6756,6837,6910,6988,7079,7168,7236,7963,8016,8074,8122,8183,8244,8311,8375,8441,8504,8563,8629,8698,8764,8816,8882,8965,9048", "endLines": "10,14,18,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118", "endColumns": "17,12,12,78,76,84,89,87,75,65,92,94,66,63,60,74,112,116,112,73,80,72,77,90,88,67,77,52,57,47,60,60,66,63,65,62,58,65,68,65,51,65,82,82,57", "endOffsets": "379,596,796,5397,5474,5559,5649,5737,5813,5879,5972,6067,6134,6198,6259,6334,6447,6564,6677,6751,6832,6905,6983,7074,7163,7231,7309,8011,8069,8117,8178,8239,8306,8370,8436,8499,8558,8624,8693,8759,8811,8877,8960,9043,9101"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8c6e034149c65399529a9cd0f184f8b\\transformed\\media3-session-1.2.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,133,211,281,348,427,509,611", "endColumns": "77,77,69,66,78,81,101,107", "endOffsets": "128,206,276,343,422,504,606,714"}, "to": {"startLines": "50,66,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3656,5177,10312,10382,10449,10528,10610,10712", "endColumns": "77,77,69,66,78,81,101,107", "endOffsets": "3729,5250,10377,10444,10523,10605,10707,10815"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d41d36b14e47c257e85459df73d97ebd\\transformed\\media3-exoplayer-1.2.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,177,239,306,384,465,552,634", "endColumns": "62,58,61,66,77,80,86,81,69", "endOffsets": "113,172,234,301,379,460,547,629,699"}, "to": {"startLines": "92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7314,7377,7436,7498,7565,7643,7724,7811,7893", "endColumns": "62,58,61,66,77,80,86,81,69", "endOffsets": "7372,7431,7493,7560,7638,7719,7806,7888,7958"}}]}]}