1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.nauh.musicplayer"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions for music streaming -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:8:5-68
14-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:8:22-65
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:9:5-77
15-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:9:22-74
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
16-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:10:5-92
16-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:10:22-89
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
17-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:11:5-77
17-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:11:22-74
18
19    <permission
19-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
20        android:name="com.nauh.musicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="com.nauh.musicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
24
25    <application
25-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:13:5-48:19
26        android:name="com.nauh.musicplayer.MusicPlayerApplication"
26-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:14:9-47
27        android:allowBackup="true"
27-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:15:9-35
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\87c47c7abe7f29a54b096c7ff4d96851\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
29        android:dataExtractionRules="@xml/data_extraction_rules"
29-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:16:9-65
30        android:debuggable="true"
31        android:extractNativeLibs="false"
32        android:fullBackupContent="@xml/backup_rules"
32-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:17:9-54
33        android:icon="@mipmap/ic_launcher"
33-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:18:9-43
34        android:label="@string/app_name"
34-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:19:9-41
35        android:roundIcon="@mipmap/ic_launcher_round"
35-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:20:9-54
36        android:supportsRtl="true"
36-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:21:9-35
37        android:testOnly="true"
38        android:theme="@style/Theme.MusicPlayer" >
38-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:22:9-49
39        <activity
39-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:24:9-32:20
40            android:name="com.nauh.musicplayer.ui.MainActivity"
40-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:25:13-44
41            android:exported="true" >
41-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:26:13-36
42            <intent-filter>
42-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:27:13-31:29
43                <action android:name="android.intent.action.MAIN" />
43-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:28:17-69
43-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:28:25-66
44
45                <category android:name="android.intent.category.LAUNCHER" />
45-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:30:17-77
45-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:30:27-74
46            </intent-filter>
47        </activity>
48        <activity
48-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:34:9-37:52
49            android:name="com.nauh.musicplayer.ui.PlayerActivity"
49-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:35:13-46
50            android:exported="false"
50-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:36:13-37
51            android:screenOrientation="portrait" />
51-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:37:13-49
52
53        <!-- Music Service -->
54        <service
54-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:40:9-47:19
55            android:name="com.nauh.musicplayer.service.MusicService"
55-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:41:13-49
56            android:exported="false"
56-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:42:13-37
57            android:foregroundServiceType="mediaPlayback" >
57-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:43:13-58
58            <intent-filter>
58-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:44:13-46:29
59                <action android:name="androidx.media3.session.MediaSessionService" />
59-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:45:17-86
59-->D:\Android Projects\MusicPlayer\app\src\main\AndroidManifest.xml:45:25-83
60            </intent-filter>
61        </service>
62
63        <provider
63-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
64            android:name="androidx.startup.InitializationProvider"
64-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
65            android:authorities="com.nauh.musicplayer.androidx-startup"
65-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
66            android:exported="false" >
66-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
67            <meta-data
67-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
68                android:name="androidx.emoji2.text.EmojiCompatInitializer"
68-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
69                android:value="androidx.startup" />
69-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18a451cbb176b9dd0e82cb4385b6bf7c\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
70            <meta-data
70-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c3a268fc5c7fb6fb6ce0ed8e9f4f7b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
71                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
71-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c3a268fc5c7fb6fb6ce0ed8e9f4f7b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
72                android:value="androidx.startup" />
72-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40c3a268fc5c7fb6fb6ce0ed8e9f4f7b\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
73            <meta-data
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
74                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
75                android:value="androidx.startup" />
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
76        </provider>
77
78        <receiver
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
79            android:name="androidx.profileinstaller.ProfileInstallReceiver"
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
80            android:directBootAware="false"
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
81            android:enabled="true"
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
82            android:exported="true"
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
83            android:permission="android.permission.DUMP" >
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
85                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
86            </intent-filter>
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
88                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
89            </intent-filter>
90            <intent-filter>
90-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
91                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
91-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
91-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
92            </intent-filter>
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
94                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
94-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
94-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5b59c9fa5d8a0ec6a6cca97ae91353e5\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
95            </intent-filter>
96        </receiver>
97    </application>
98
99</manifest>
